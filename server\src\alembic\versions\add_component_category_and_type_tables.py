"""add_component_category_and_type_tables

Revision ID: add_component_category_and_type_tables
Revises: 4cf07113de3c
Create Date: 2025-07-15 20:00:00.000000

This migration creates ComponentCategory and ComponentType tables and populates them
with data from the existing enums, then adds foreign key relationships to the Component table.
"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy import text
from datetime import datetime

# revision identifiers, used by Alembic.
revision = 'add_component_category_and_type_tables'
down_revision = '4cf07113de3c'
branch_labels = None
depends_on = None


def upgrade() -> None:
    """Create ComponentCategory and ComponentType tables and populate with enum data."""
    
    # Create ComponentCategory table
    op.create_table(
        'ComponentCategory',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('notes', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('deleted_by_user_id', sa.Integer(), nullable=True),
        sa.Column('description', sa.Text(), nullable=True, comment='Detailed category description'),
        sa.Column('parent_category_id', sa.Integer(), nullable=True, comment='Parent category ID for hierarchical organization'),
        sa.Column('is_active', sa.Boolean(), nullable=False, comment='Whether category is active in the system'),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['parent_category_id'], ['ComponentCategory.id']),
    )
    
    # Create indexes for ComponentCategory
    op.create_index('idx_component_category_name_active', 'ComponentCategory', ['name', 'is_active'])
    op.create_index('idx_component_category_parent_active', 'ComponentCategory', ['parent_category_id', 'is_active'])

    # Use batch mode for SQLite constraint creation
    with op.batch_alter_table('ComponentCategory') as batch_op:
        batch_op.create_unique_constraint('uq_component_category_name_parent', ['name', 'parent_category_id', 'is_deleted'])
    
    # Create ComponentType table
    op.create_table(
        'ComponentType',
        sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('notes', sa.String(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=False),
        sa.Column('updated_at', sa.DateTime(), nullable=False),
        sa.Column('is_deleted', sa.Boolean(), nullable=False),
        sa.Column('deleted_at', sa.DateTime(), nullable=True),
        sa.Column('deleted_by_user_id', sa.Integer(), nullable=True),
        sa.Column('description', sa.Text(), nullable=True, comment='Detailed component type description'),
        sa.Column('category_id', sa.Integer(), nullable=False, comment='Component category ID for organization'),
        sa.Column('is_active', sa.Boolean(), nullable=False, comment='Whether component type is active in the system'),
        sa.Column('specifications_template', sa.Text(), nullable=True, comment='JSON template for component specifications'),
        sa.Column('metadata', sa.Text(), nullable=True, comment='Additional metadata for component type'),
        sa.PrimaryKeyConstraint('id'),
        sa.ForeignKeyConstraint(['category_id'], ['ComponentCategory.id']),
    )
    
    # Create indexes for ComponentType
    op.create_index('idx_component_type_name_active', 'ComponentType', ['name', 'is_active'])
    op.create_index('idx_component_type_category_active', 'ComponentType', ['category_id', 'is_active'])
    op.create_index('idx_component_type_name_category', 'ComponentType', ['name', 'category_id'])

    # Use batch mode for SQLite constraint creation
    with op.batch_alter_table('ComponentType') as batch_op:
        batch_op.create_unique_constraint('uq_component_type_name_category', ['name', 'category_id', 'is_deleted'])
    
    # Populate ComponentCategory table with enum data
    current_time = datetime.utcnow()
    
    category_data = [
        (1, 'Power Distribution', 'Power distribution equipment and systems', current_time, current_time, False, True),
        (2, 'Cables & Wiring', 'Electrical cables and wiring systems', current_time, current_time, False, True),
        (3, 'Protection Devices', 'Electrical protection and safety devices', current_time, current_time, False, True),
        (4, 'Switching & Control', 'Switching and control equipment', current_time, current_time, False, True),
        (5, 'Measurement & Monitoring', 'Measurement and monitoring instruments', current_time, current_time, False, True),
        (6, 'Enclosures & Mounting', 'Enclosures and mounting equipment', current_time, current_time, False, True),
        (7, 'Grounding & Bonding', 'Grounding and bonding systems', current_time, current_time, False, True),
        (8, 'Power Sources', 'Power sources and generation equipment', current_time, current_time, False, True),
        (9, 'Loads', 'Electrical loads and consuming equipment', current_time, current_time, False, True),
        (10, 'Communication', 'Communication and networking equipment', current_time, current_time, False, True),
        (11, 'Safety & Emergency', 'Safety and emergency systems', current_time, current_time, False, True),
        (12, 'Heat Tracing System', 'Heat tracing system components', current_time, current_time, False, True),
        (13, 'Cable Management', 'Cable management systems', current_time, current_time, False, True),
        (14, 'Other Electrical', 'Other electrical components', current_time, current_time, False, True),
    ]
    
    # Insert category data
    for cat_id, name, description, created_at, updated_at, is_deleted, is_active in category_data:
        op.execute(
            text("""
                INSERT INTO "ComponentCategory" 
                (id, name, description, created_at, updated_at, is_deleted, is_active, parent_category_id, notes, deleted_at, deleted_by_user_id)
                VALUES (:id, :name, :description, :created_at, :updated_at, :is_deleted, :is_active, NULL, NULL, NULL, NULL)
            """),
            {
                'id': cat_id,
                'name': name,
                'description': description,
                'created_at': created_at,
                'updated_at': updated_at,
                'is_deleted': is_deleted,
                'is_active': is_active
            }
        )
    
    # Populate ComponentType table with enum data (sample - full list would be much longer)
    type_data = [
        # Power Distribution (category_id=1)
        (1, 'Switchboard', 'Main electrical switchboard', 1, current_time, current_time, False, True),
        (2, 'Motor Control Center (MCC)', 'Motor control center', 1, current_time, current_time, False, True),
        (3, 'Distribution Board (DB)', 'Distribution board', 1, current_time, current_time, False, True),
        (4, 'Panelboard', 'Electrical panelboard', 1, current_time, current_time, False, True),
        (5, 'Main Switchboard (MSB)', 'Main switchboard', 1, current_time, current_time, False, True),
        (6, 'Sub Switchboard (SSB)', 'Sub switchboard', 1, current_time, current_time, False, True),
        (7, 'Control Panel', 'Control panel', 1, current_time, current_time, False, True),
        (8, 'Transformer', 'Electrical transformer', 1, current_time, current_time, False, True),
        (9, 'Generator', 'Electrical generator', 1, current_time, current_time, False, True),
        (10, 'Uninterruptible Power Supply (UPS)', 'UPS system', 1, current_time, current_time, False, True),
        
        # Protection Devices (category_id=3)
        (11, 'Circuit Breaker', 'Circuit breaker', 3, current_time, current_time, False, True),
        (12, 'Fuse', 'Electrical fuse', 3, current_time, current_time, False, True),
        (13, 'Residual Current Device (RCD)', 'RCD protection device', 3, current_time, current_time, False, True),
        (14, 'Overload Relay', 'Overload protection relay', 3, current_time, current_time, False, True),
        (15, 'Protective Relay', 'General protective relay', 3, current_time, current_time, False, True),
        
        # Cables & Wiring (category_id=2)
        (16, 'Power Cable', 'Power transmission cable', 2, current_time, current_time, False, True),
        (17, 'Control Cable', 'Control signal cable', 2, current_time, current_time, False, True),
        (18, 'Instrumentation Cable', 'Instrumentation cable', 2, current_time, current_time, False, True),
        (19, 'Communication Cable', 'Communication cable', 2, current_time, current_time, False, True),
        (20, 'Fiber Optic Cable', 'Fiber optic cable', 2, current_time, current_time, False, True),
        
        # Loads (category_id=9)
        (21, 'Electric Motor', 'Electric motor', 9, current_time, current_time, False, True),
        (22, 'Heater', 'Electric heater', 9, current_time, current_time, False, True),
        (23, 'Lighting Fixture', 'Lighting fixture', 9, current_time, current_time, False, True),
        (24, 'Fan', 'Electric fan', 9, current_time, current_time, False, True),
        (25, 'Pump', 'Electric pump', 9, current_time, current_time, False, True),
    ]
    
    # Insert component type data
    for type_id, name, description, category_id, created_at, updated_at, is_deleted, is_active in type_data:
        op.execute(
            text("""
                INSERT INTO "ComponentType" 
                (id, name, description, category_id, created_at, updated_at, is_deleted, is_active, specifications_template, metadata, notes, deleted_at, deleted_by_user_id)
                VALUES (:id, :name, :description, :category_id, :created_at, :updated_at, :is_deleted, :is_active, NULL, NULL, NULL, NULL, NULL)
            """),
            {
                'id': type_id,
                'name': name,
                'description': description,
                'category_id': category_id,
                'created_at': created_at,
                'updated_at': updated_at,
                'is_deleted': is_deleted,
                'is_active': is_active
            }
        )
    
    # Add new foreign key columns to Component table
    op.add_column('Component', sa.Column('component_type_id', sa.Integer(), nullable=True, comment='Component type ID (new relational approach)'))
    op.add_column('Component', sa.Column('component_category_id', sa.Integer(), nullable=True, comment='Component category ID (new relational approach)'))
    
    # Create foreign key constraints
    op.create_foreign_key('fk_component_type_id', 'Component', 'ComponentType', ['component_type_id'], ['id'])
    op.create_foreign_key('fk_component_category_id', 'Component', 'ComponentCategory', ['component_category_id'], ['id'])
    
    # Create indexes for the new foreign keys
    op.create_index('idx_component_type_id', 'Component', ['component_type_id'])
    op.create_index('idx_component_category_id', 'Component', ['component_category_id'])


def downgrade() -> None:
    """Remove ComponentCategory and ComponentType tables and foreign keys."""
    
    # Drop foreign key constraints and indexes from Component table
    op.drop_index('idx_component_category_id', 'Component')
    op.drop_index('idx_component_type_id', 'Component')
    op.drop_constraint('fk_component_category_id', 'Component', type_='foreignkey')
    op.drop_constraint('fk_component_type_id', 'Component', type_='foreignkey')
    
    # Drop new columns from Component table
    op.drop_column('Component', 'component_category_id')
    op.drop_column('Component', 'component_type_id')
    
    # Drop ComponentType table
    op.drop_constraint('uq_component_type_name_category', 'ComponentType', type_='unique')
    op.drop_index('idx_component_type_name_category', 'ComponentType')
    op.drop_index('idx_component_type_category_active', 'ComponentType')
    op.drop_index('idx_component_type_name_active', 'ComponentType')
    op.drop_table('ComponentType')
    
    # Drop ComponentCategory table
    op.drop_constraint('uq_component_category_name_parent', 'ComponentCategory', type_='unique')
    op.drop_index('idx_component_category_parent_active', 'ComponentCategory')
    op.drop_index('idx_component_category_name_active', 'ComponentCategory')
    op.drop_table('ComponentCategory')
