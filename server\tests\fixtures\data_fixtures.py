#!/usr/bin/env python3
"""Provide test data for various calculations."""

import json
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, patch

import pytest
from fastapi import status
from fastapi.testclient import TestClient

from src.core.enums.electrical_enums import ComponentCategoryType, ComponentType
from src.core.errors.exceptions import BusinessLogicError, NotFoundError, ValidationError
from src.core.models.general.component import Component
from src.core.schemas.general.component_schemas import (
    ComponentCreateSchema,
    ComponentReadSchema,
    ComponentSearchSchema,
    ComponentUpdateSchema,
)



@pytest.fixture(scope="function")
def tank_heat_loss_test_data() -> dict[str, Any]:
    """Provide test data for tank heat loss calculations."""
    return {
        "vertical_cylinder": {
            "diameter_ft": 8.0,
            "height_ft": 15.0,
            "maintain_temp_f": 160.0,
            "ambient_temp_f": 70.0,
            "insulation_thickness_inches": 2.5,
            "insulation_material": "fiberglass",
            "is_outdoor": True,
            "wind_speed_mph": 15.0,
        },
        "horizontal_cylinder": {
            "diameter_ft": 6.0,
            "height_ft": 12.0,
            "maintain_temp_f": 150.0,
            "ambient_temp_f": 50.0,
            "insulation_thickness_inches": 1.5,
            "insulation_material": "fiberglass",
            "is_outdoor": False,
            "wind_speed_mph": 0.0,
        },
        "sphere": {
            "diameter_ft": 10.0,
            "height_ft": 10.0,  # Not used for sphere
            "maintain_temp_f": 180.0,
            "ambient_temp_f": 60.0,
            "insulation_thickness_inches": 3.0,
            "insulation_material": "mineral_wool",
            "is_outdoor": True,
            "wind_speed_mph": 20.0,
        },
    }


@pytest.fixture(scope="function")
def power_calculation_test_data() -> dict[str, Any]:
    """Provide test data for power calculations."""
    return {
        "electrical_power": {
            "voltage": 230.0,
            "current": 10.0,
            "resistance": 23.0,
            "power_factor": 0.85,
        },
        "heat_tracing_power": {
            "heat_loss_per_meter": 25.0,
            "length": 100.0,
            "safety_factor": 1.3,
            "efficiency": 0.9,
            "voltage": 230.0,
        },
        "three_phase_power": {
            "line_voltage": 400.0,
            "line_current": 15.0,
            "power_factor": 0.9,
            "phases": 3,
        },
    }


@pytest.fixture(scope="function")
def pipe_sizing_test_data() -> dict[str, Any]:
    """Provide test data for pipe sizing calculations."""
    return {
        "standard_sizes": [
            "1/2",
            "3/4",
            "1",
            "1-1/4",
            "1-1/2",
            "2",
            "3",
            "4",
            "6",
            "8",
        ],
        "nps_to_dn_mappings": {
            "1/2": 15,
            "3/4": 20,
            "1": 25,
            "2": 50,
            "3": 80,
            "4": 100,
            "6": 150,
            "8": 200,
        },
        "valve_allowances": {
            "light_flanged": {"1": 2.0, "2": 2.5, "3": 3.0, "4": 4.0},
            "heavy_flanged": {"1": 3.0, "2": 4.0, "3": 5.0, "4": 6.0},
        },
    }

@pytest.fixture(scope="function")
def sample_component_data() -> dict[str, Any]:
    """Sample component data for testing."""
    return {
        "name": "Test Circuit Breaker",
        "manufacturer": "ABB",
        "model_number": "S203-C16",
        "description": "3-pole miniature circuit breaker, 16A, C-curve",
        "component_type": ComponentType.CIRCUIT_BREAKER.value,
        "category": ComponentCategoryType.PROTECTION_DEVICES.value,
        "specifications": {
            "electrical": {
                "current_rating": "16A",
                "voltage_rating": "400V",
                "breaking_capacity": "6kA",
                "curve_type": "C"
            },
            "standards_compliance": ["IEC-60898-1", "EN-60898-1"]
        },
        "unit_price": "25.50",
        "currency": "EUR",
        "supplier": "Electrical Supplies Ltd",
        "part_number": "ABB-S203-C16",
        "weight_kg": 0.15,
        "dimensions": {
            "length": 18,
            "width": 85,
            "height": 78
        },
        "is_active": True,
        "is_preferred": False,
        "stock_status": "available",
        "version": "1.0",
        "metadata": {
            "test_data": True,
            "created_for": "unit_testing"
        }
    }

@pytest.fixture(scope="function")
def large_component_dataset(db_session):
    """Create a large dataset of components for performance testing."""
    components = []
    
    # Create 1000 components for performance testing
    for i in range(1000):
        component = Component(
            name=f"Component {i:04d}",
            manufacturer=f"Manufacturer {i % 10}",
            model_number=f"MODEL-{i:04d}",
            component_type=ComponentType.CIRCUIT_BREAKER if i % 2 == 0 else ComponentType.FUSE,
            category=ComponentCategoryType.PROTECTION_DEVICES,
            unit_price=Decimal(f"{10 + (i % 100)}.50"),
            currency="EUR",
            supplier=f"Supplier {i % 5}",
            part_number=f"PART-{i:04d}",
            description=f"Test component {i} for performance testing",
            specifications=json.dumps({
                "electrical": {
                    "voltage_rating": f"{400 + (i % 100)}V",
                    "current_rating": f"{16 + (i % 50)}A"
                },
                "physical": {
                    "width": f"{50 + (i % 20)}mm",
                    "height": f"{80 + (i % 30)}mm"
                }
            }),
            is_preferred=(i % 10 == 0),
            is_active=True
        )
        components.append(component)
    
    # Batch insert for better performance
    db_session.add_all(components)
    db_session.commit()
    
    return components