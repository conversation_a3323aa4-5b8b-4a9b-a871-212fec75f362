#!/usr/bin/env python3
"""Tests for Component Category functionality.

This module provides comprehensive tests for component category management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentCategory model validation and business logic
- ComponentCategoryRepository data access operations
- ComponentCategoryService business logic and validation
- Component Category API endpoints and error handling
- Hierarchical operations and tree management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_category_repository import ComponentCategoryRepository
from src.core.services.general.component_category_service import ComponentCategoryService
from src.core.schemas.general.component_category_schemas import (
    ComponentCategoryCreateSchema,
    ComponentCategoryUpdateSchema,
    ComponentCategorySearchSchema,
)
from src.core.exceptions import BusinessLogicError, NotFoundError, ValidationError
from src.core.utils.pagination import PaginationParams


class TestComponentCategoryModel:
    """Test ComponentCategory model functionality."""

    def test_component_category_creation(self, db_session: Session):
        """Test creating a ComponentCategory instance."""
        category = ComponentCategory(
            name="Test Category",
            description="Test description",
            is_active=True,
        )
        
        db_session.add(category)
        db_session.commit()
        
        assert category.id is not None
        assert category.name == "Test Category"
        assert category.description == "Test description"
        assert category.is_active is True
        assert category.is_root_category is True
        assert category.level == 0

    def test_hierarchical_category_creation(self, db_session: Session):
        """Test creating hierarchical categories."""
        # Create parent category
        parent = ComponentCategory(
            name="Parent Category",
            description="Parent description",
            is_active=True,
        )
        db_session.add(parent)
        db_session.commit()
        
        # Create child category
        child = ComponentCategory(
            name="Child Category",
            description="Child description",
            parent_category_id=parent.id,
            is_active=True,
        )
        db_session.add(child)
        db_session.commit()
        
        assert child.parent_category_id == parent.id
        assert child.is_root_category is False
        assert child.level == 1
        assert child.full_path == "Parent Category > Child Category"

    def test_category_properties(self, db_session: Session):
        """Test category computed properties."""
        category = ComponentCategory(
            name="Test Category",
            description="Test description",
            is_active=True,
        )
        db_session.add(category)
        db_session.commit()
        
        assert category.component_count == 0
        assert category.has_children is False
        assert category.can_delete() == (True, None)

    def test_category_soft_delete(self, db_session: Session):
        """Test category soft delete functionality."""
        category = ComponentCategory(
            name="Test Category",
            description="Test description",
            is_active=True,
        )
        db_session.add(category)
        db_session.commit()
        
        # Test successful soft delete
        result = category.soft_delete(user_id=1)
        assert result is True
        assert category.is_deleted is True
        assert category.deleted_at is not None
        assert category.deleted_by_user_id == 1
        assert category.is_active is False


class TestComponentCategoryRepository:
    """Test ComponentCategoryRepository functionality."""

    @pytest.fixture
    def repository(self, db_session: Session) -> ComponentCategoryRepository:
        """Create ComponentCategoryRepository instance."""
        return ComponentCategoryRepository(db_session)

    @pytest.fixture
    def sample_category(self, db_session: Session) -> ComponentCategory:
        """Create a sample category for testing."""
        category = ComponentCategory(
            name="Sample Category",
            description="Sample description",
            is_active=True,
        )
        db_session.add(category)
        db_session.commit()
        return category

    def test_create_category(self, repository: ComponentCategoryRepository):
        """Test creating a category through repository."""
        category_data = {
            "name": "New Category",
            "description": "New description",
            "is_active": True,
        }
        
        category = repository.create(category_data)
        
        assert category.id is not None
        assert category.name == "New Category"
        assert category.description == "New description"
        assert category.is_active is True

    def test_get_by_id(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test retrieving category by ID."""
        category = repository.get_by_id(sample_category.id)
        
        assert category is not None
        assert category.id == sample_category.id
        assert category.name == sample_category.name

    def test_get_by_name(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test retrieving category by name."""
        category = repository.get_by_name("Sample Category")
        
        assert category is not None
        assert category.id == sample_category.id
        assert category.name == "Sample Category"

    def test_get_root_categories(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test retrieving root categories."""
        categories = repository.get_root_categories()
        
        assert len(categories) >= 1
        assert any(cat.id == sample_category.id for cat in categories)
        assert all(cat.parent_category_id is None for cat in categories)

    def test_search_categories(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test searching categories."""
        search_schema = ComponentCategorySearchSchema(
            search_term="Sample",
            is_active=True,
        )
        pagination = PaginationParams(page=1, limit=10)
        
        categories, total_count = repository.search_categories(search_schema, pagination)
        
        assert total_count >= 1
        assert len(categories) >= 1
        assert any(cat.id == sample_category.id for cat in categories)

    def test_update_category(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test updating a category."""
        update_data = {
            "name": "Updated Category",
            "description": "Updated description",
        }
        
        updated_category = repository.update(sample_category.id, update_data)
        
        assert updated_category.name == "Updated Category"
        assert updated_category.description == "Updated description"

    def test_delete_category(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test deleting a category."""
        result = repository.delete(sample_category.id)
        
        assert result is True
        
        # Verify category is soft deleted
        category = repository.get_by_id(sample_category.id)
        assert category is None  # Should not be returned due to soft delete filter


class TestComponentCategoryService:
    """Test ComponentCategoryService functionality."""

    @pytest.fixture
    def mock_repository(self) -> Mock:
        """Create mock repository."""
        return Mock(spec=ComponentCategoryRepository)

    @pytest.fixture
    def service(self, mock_repository: Mock) -> ComponentCategoryService:
        """Create ComponentCategoryService instance."""
        return ComponentCategoryService(mock_repository)

    @pytest.fixture
    def sample_category_data(self) -> ComponentCategoryCreateSchema:
        """Create sample category data."""
        return ComponentCategoryCreateSchema(
            name="Test Category",
            description="Test description",
            is_active=True,
        )

    def test_create_category_success(self, service: ComponentCategoryService, mock_repository: Mock, sample_category_data: ComponentCategoryCreateSchema):
        """Test successful category creation."""
        # Setup mock
        mock_category = ComponentCategory(
            id=1,
            name=sample_category_data.name,
            description=sample_category_data.description,
            is_active=sample_category_data.is_active,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        mock_repository.get_by_name.return_value = None
        mock_repository.create.return_value = mock_category
        
        # Test creation
        result = service.create_category(sample_category_data)
        
        assert result.id == 1
        assert result.name == "Test Category"
        assert result.description == "Test description"
        mock_repository.create.assert_called_once()

    def test_create_category_duplicate_name(self, service: ComponentCategoryService, mock_repository: Mock, sample_category_data: ComponentCategoryCreateSchema):
        """Test category creation with duplicate name."""
        # Setup mock to return existing category
        existing_category = ComponentCategory(
            id=1,
            name=sample_category_data.name,
            description="Existing description",
            is_active=True,
        )
        mock_repository.get_by_name.return_value = existing_category
        
        # Test creation should fail
        with pytest.raises(BusinessLogicError, match="already exists"):
            service.create_category(sample_category_data)

    def test_get_category_success(self, service: ComponentCategoryService, mock_repository: Mock):
        """Test successful category retrieval."""
        # Setup mock
        mock_category = ComponentCategory(
            id=1,
            name="Test Category",
            description="Test description",
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        mock_repository.get_by_id.return_value = mock_category
        
        # Test retrieval
        result = service.get_category(1)
        
        assert result.id == 1
        assert result.name == "Test Category"
        mock_repository.get_by_id.assert_called_once_with(1)

    def test_get_category_not_found(self, service: ComponentCategoryService, mock_repository: Mock):
        """Test category retrieval when not found."""
        # Setup mock to return None
        mock_repository.get_by_id.return_value = None
        
        # Test retrieval should fail
        with pytest.raises(NotFoundError, match="not found"):
            service.get_category(999)

    def test_update_category_success(self, service: ComponentCategoryService, mock_repository: Mock):
        """Test successful category update."""
        # Setup mock
        existing_category = ComponentCategory(
            id=1,
            name="Old Name",
            description="Old description",
            is_active=True,
        )
        updated_category = ComponentCategory(
            id=1,
            name="New Name",
            description="New description",
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        mock_repository.get_by_id.return_value = existing_category
        mock_repository.validate_hierarchy.return_value = True
        mock_repository.get_by_name.return_value = None
        mock_repository.update.return_value = updated_category
        
        update_data = ComponentCategoryUpdateSchema(
            name="New Name",
            description="New description",
        )
        
        # Test update
        result = service.update_category(1, update_data)
        
        assert result.name == "New Name"
        assert result.description == "New description"
        mock_repository.update.assert_called_once()

    def test_delete_category_success(self, service: ComponentCategoryService, mock_repository: Mock):
        """Test successful category deletion."""
        # Setup mock
        mock_category = ComponentCategory(
            id=1,
            name="Test Category",
            description="Test description",
            is_active=True,
        )
        mock_category.can_delete = Mock(return_value=(True, None))
        mock_category.soft_delete = Mock(return_value=True)
        mock_repository.get_by_id.return_value = mock_category
        mock_repository.db_session = Mock()
        
        # Test deletion
        result = service.delete_category(1, user_id=1)
        
        assert result is True
        mock_category.soft_delete.assert_called_once_with(1)
        mock_repository.db_session.commit.assert_called_once()

    def test_delete_category_with_dependencies(self, service: ComponentCategoryService, mock_repository: Mock):
        """Test category deletion when it has dependencies."""
        # Setup mock
        mock_category = ComponentCategory(
            id=1,
            name="Test Category",
            description="Test description",
            is_active=True,
        )
        mock_category.can_delete = Mock(return_value=(False, "Has active component types"))
        mock_repository.get_by_id.return_value = mock_category
        
        # Test deletion should fail
        with pytest.raises(BusinessLogicError, match="Cannot delete category"):
            service.delete_category(1, user_id=1)

    def test_list_categories(self, service: ComponentCategoryService, mock_repository: Mock):
        """Test listing categories with pagination."""
        # Setup mock
        mock_categories = [
            ComponentCategory(id=1, name="Category 1", is_active=True),
            ComponentCategory(id=2, name="Category 2", is_active=True),
        ]
        mock_repository.search_categories.return_value = (mock_categories, 2)
        
        # Test listing
        result = service.list_categories()
        
        assert result.total_count == 2
        assert len(result.categories) == 2
        assert result.categories[0].name == "Category 1"
        assert result.categories[1].name == "Category 2"


class TestComponentCategoryAPI:
    """Test Component Category API endpoints."""

    def test_create_category_endpoint(self, client: TestClient, auth_headers: Dict[str, str]):
        """Test POST /component-categories/ endpoint."""
        category_data = {
            "name": "API Test Category",
            "description": "API test description",
            "is_active": True,
        }
        
        response = client.post(
            "/api/v1/component-categories/",
            json=category_data,
            headers=auth_headers,
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "API Test Category"
        assert data["description"] == "API test description"
        assert data["is_active"] is True

    def test_get_category_endpoint(self, client: TestClient, auth_headers: Dict[str, str]):
        """Test GET /component-categories/{id} endpoint."""
        # First create a category
        category_data = {
            "name": "Get Test Category",
            "description": "Get test description",
            "is_active": True,
        }
        
        create_response = client.post(
            "/api/v1/component-categories/",
            json=category_data,
            headers=auth_headers,
        )
        category_id = create_response.json()["id"]
        
        # Then retrieve it
        response = client.get(
            f"/api/v1/component-categories/{category_id}",
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == category_id
        assert data["name"] == "Get Test Category"

    def test_list_categories_endpoint(self, client: TestClient, auth_headers: Dict[str, str]):
        """Test GET /component-categories/ endpoint."""
        response = client.get(
            "/api/v1/component-categories/",
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "categories" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data

    def test_update_category_endpoint(self, client: TestClient, auth_headers: Dict[str, str]):
        """Test PUT /component-categories/{id} endpoint."""
        # First create a category
        category_data = {
            "name": "Update Test Category",
            "description": "Update test description",
            "is_active": True,
        }
        
        create_response = client.post(
            "/api/v1/component-categories/",
            json=category_data,
            headers=auth_headers,
        )
        category_id = create_response.json()["id"]
        
        # Then update it
        update_data = {
            "name": "Updated Category Name",
            "description": "Updated description",
        }
        
        response = client.put(
            f"/api/v1/component-categories/{category_id}",
            json=update_data,
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Category Name"
        assert data["description"] == "Updated description"

    def test_delete_category_endpoint(self, client: TestClient, auth_headers: Dict[str, str]):
        """Test DELETE /component-categories/{id} endpoint."""
        # First create a category
        category_data = {
            "name": "Delete Test Category",
            "description": "Delete test description",
            "is_active": True,
        }
        
        create_response = client.post(
            "/api/v1/component-categories/",
            json=category_data,
            headers=auth_headers,
        )
        category_id = create_response.json()["id"]
        
        # Then delete it
        response = client.delete(
            f"/api/v1/component-categories/{category_id}",
            headers=auth_headers,
        )
        
        assert response.status_code == 204
        
        # Verify it's deleted
        get_response = client.get(
            f"/api/v1/component-categories/{category_id}",
            headers=auth_headers,
        )
        assert get_response.status_code == 404

    def test_get_category_tree_endpoint(self, client: TestClient, auth_headers: Dict[str, str]):
        """Test GET /component-categories/tree endpoint."""
        response = client.get(
            "/api/v1/component-categories/tree",
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "tree" in data
        assert "total_categories" in data
        assert "max_depth" in data
