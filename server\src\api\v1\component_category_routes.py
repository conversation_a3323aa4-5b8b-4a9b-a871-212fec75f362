#!/usr/bin/env python3
"""Component Category API Routes for Ultimate Electrical Designer.

This module provides REST API endpoints for component category management operations,
including CRUD operations, hierarchical operations, and category organization for
electrical component catalog management.

Key Features:
- Complete CRUD operations with comprehensive validation
- Hierarchical category operations and tree management
- Advanced search and filtering with pagination
- Professional error handling and logging
- OpenAPI documentation with detailed examples
- Authentication and authorization integration
- Performance monitoring and caching
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, Query, status
from fastapi.responses import JSONResponse

from src.core.schemas.general.component_category_schemas import (
    ComponentCategoryCreateSchema,
    ComponentCategoryListResponseSchema,
    ComponentCategoryReadSchema,
    ComponentCategorySearchSchema,
    ComponentCategorySummarySchema,
    ComponentCategoryTreeResponseSchema,
    ComponentCategoryUpdateSchema,
)
from src.core.schemas.response_schemas import ErrorResponseSchema
from src.core.security.enhanced_dependencies import require_authenticated_user
from src.core.services.dependencies import get_component_category_service
from src.core.services.general.component_category_service import ComponentCategoryService
from src.core.utils.decorators import handle_api_errors, monitor_api_performance
from src.core.utils.logging import get_logger
from src.core.utils.pagination import PaginationParams

logger = get_logger(__name__)

router = APIRouter(
    prefix="/component-categories",
    tags=["Component Categories"],
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
        status.HTTP_403_FORBIDDEN: {
            "description": "Insufficient permissions",
            "model": ErrorResponseSchema,
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "Internal server error",
            "model": ErrorResponseSchema,
        },
    },
)


@router.post(
    "/",
    response_model=ComponentCategoryReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create Component Category",
    description="Create a new component category with hierarchical support",
    responses={
        status.HTTP_201_CREATED: {
            "description": "Category created successfully",
            "model": ComponentCategoryReadSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid category data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_409_CONFLICT: {
            "description": "Category already exists",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("create_component_category")
@monitor_api_performance("create_component_category")
async def create_component_category(
    category_data: ComponentCategoryCreateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    category_service: ComponentCategoryService = Depends(get_component_category_service),
) -> ComponentCategoryReadSchema:
    """Create a new component category.

    This endpoint creates a new category in the electrical component catalog with
    comprehensive validation and hierarchical organization support.

    Args:
        category_data: Category creation data
        current_user: Current authenticated user
        category_service: Component category service dependency

    Returns:
        ComponentCategoryReadSchema: Created category data
    """
    logger.info(f"Creating component category: {category_data.name}")

    category = category_service.create_category(category_data)
    
    logger.info(f"Created component category: {category.id}")
    return category


@router.get(
    "/{category_id}",
    response_model=ComponentCategoryReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Get Component Category",
    description="Get component category by ID with full details",
    responses={
        status.HTTP_200_OK: {
            "description": "Category retrieved successfully",
            "model": ComponentCategoryReadSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Category not found",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("get_component_category")
@monitor_api_performance("get_component_category")
async def get_component_category(
    category_id: int,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    category_service: ComponentCategoryService = Depends(get_component_category_service),
) -> ComponentCategoryReadSchema:
    """Get component category by ID.

    Args:
        category_id: Category ID
        current_user: Current authenticated user
        category_service: Component category service dependency

    Returns:
        ComponentCategoryReadSchema: Category data
    """
    logger.debug(f"Retrieving component category: {category_id}")

    category = category_service.get_category(category_id)
    
    logger.debug(f"Retrieved component category: {category_id}")
    return category


@router.put(
    "/{category_id}",
    response_model=ComponentCategoryReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Update Component Category",
    description="Update component category with validation",
    responses={
        status.HTTP_200_OK: {
            "description": "Category updated successfully",
            "model": ComponentCategoryReadSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Category not found",
            "model": ErrorResponseSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid update data",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("update_component_category")
@monitor_api_performance("update_component_category")
async def update_component_category(
    category_id: int,
    category_data: ComponentCategoryUpdateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    category_service: ComponentCategoryService = Depends(get_component_category_service),
) -> ComponentCategoryReadSchema:
    """Update component category.

    Args:
        category_id: Category ID
        category_data: Category update data
        current_user: Current authenticated user
        category_service: Component category service dependency

    Returns:
        ComponentCategoryReadSchema: Updated category data
    """
    logger.info(f"Updating component category: {category_id}")

    category = category_service.update_category(category_id, category_data)
    
    logger.info(f"Updated component category: {category_id}")
    return category


@router.delete(
    "/{category_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Component Category",
    description="Soft delete component category with dependency checking",
    responses={
        status.HTTP_204_NO_CONTENT: {
            "description": "Category deleted successfully",
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Category not found",
            "model": ErrorResponseSchema,
        },
        status.HTTP_409_CONFLICT: {
            "description": "Category has dependencies",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("delete_component_category")
@monitor_api_performance("delete_component_category")
async def delete_component_category(
    category_id: int,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    category_service: ComponentCategoryService = Depends(get_component_category_service),
) -> JSONResponse:
    """Delete component category.

    Args:
        category_id: Category ID
        current_user: Current authenticated user
        category_service: Component category service dependency

    Returns:
        JSONResponse: Empty response with 204 status
    """
    logger.info(f"Deleting component category: {category_id}")

    user_id = current_user.get("id")
    category_service.delete_category(category_id, user_id)
    
    logger.info(f"Deleted component category: {category_id}")
    return JSONResponse(status_code=status.HTTP_204_NO_CONTENT, content=None)


@router.get(
    "/",
    response_model=ComponentCategoryListResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="List Component Categories",
    description="List component categories with filtering and pagination",
    responses={
        status.HTTP_200_OK: {
            "description": "Categories retrieved successfully",
            "model": ComponentCategoryListResponseSchema,
        },
    },
)
@handle_api_errors("list_component_categories")
@monitor_api_performance("list_component_categories")
async def list_component_categories(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    search_term: Optional[str] = Query(None, description="Search term"),
    parent_category_id: Optional[int] = Query(None, description="Filter by parent category"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    category_service: ComponentCategoryService = Depends(get_component_category_service),
) -> ComponentCategoryListResponseSchema:
    """List component categories with filtering and pagination.

    Args:
        page: Page number (1-based)
        size: Number of items per page
        search_term: Search term for name or description
        parent_category_id: Filter by parent category
        is_active: Filter by active status
        current_user: Current authenticated user
        category_service: Component category service dependency

    Returns:
        ComponentCategoryListResponseSchema: Paginated category list
    """
    logger.debug("Listing component categories")

    # Build search schema
    search_schema = ComponentCategorySearchSchema(
        search_term=search_term,
        parent_category_id=parent_category_id,
        is_active=is_active,
    )

    # Build pagination
    pagination = PaginationParams(page=page, limit=size)

    # Get categories
    categories = category_service.list_categories(search_schema, pagination)
    
    logger.debug(f"Listed {len(categories.categories)} categories")
    return categories


@router.get(
    "/tree",
    response_model=ComponentCategoryTreeResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Get Category Tree",
    description="Get hierarchical category tree structure",
    responses={
        status.HTTP_200_OK: {
            "description": "Category tree retrieved successfully",
            "model": ComponentCategoryTreeResponseSchema,
        },
    },
)
@handle_api_errors("get_category_tree")
@monitor_api_performance("get_category_tree")
async def get_category_tree(
    root_id: Optional[int] = Query(None, description="Root category ID"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    category_service: ComponentCategoryService = Depends(get_component_category_service),
) -> ComponentCategoryTreeResponseSchema:
    """Get hierarchical category tree.

    Args:
        root_id: Optional root category ID to start from
        current_user: Current authenticated user
        category_service: Component category service dependency

    Returns:
        ComponentCategoryTreeResponseSchema: Hierarchical category tree
    """
    logger.debug(f"Getting category tree from root: {root_id}")

    tree = category_service.get_category_tree(root_id)
    
    logger.debug(f"Retrieved category tree with {tree.total_categories} categories")
    return tree
