#!/usr/bin/env python3
"""Tests for Component Category functionality.

This module provides comprehensive tests for component category management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentCategory model validation and business logic
- ComponentCategoryRepository data access operations
- ComponentCategoryService business logic and validation
- Component Category API endpoints and error handling
- Hierarchical operations and tree management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_category_repository import ComponentCategoryRepository
from src.core.services.general.component_category_service import ComponentCategoryService
from src.core.schemas.general.component_category_schemas import (
    ComponentCategoryCreateSchema,
    ComponentCategoryUpdateSchema,
    ComponentCategorySearchSchema,
)
from src.core.errors.exceptions import BusinessLogicError, NotFoundError, ValidationError
from src.core.utils.pagination_utils import PaginationParams


class TestComponentCategoryRepository:
    """Test ComponentCategoryRepository functionality."""

    @pytest.fixture
    def repository(self, db_session: Session) -> ComponentCategoryRepository:
        """Create ComponentCategoryRepository instance."""
        return ComponentCategoryRepository(db_session)

    @pytest.fixture
    def sample_category(self, db_session: Session) -> ComponentCategory:
        """Create a sample category for testing."""
        category = ComponentCategory(
            name="Sample Category",
            description="Sample description",
            is_active=True,
        )
        db_session.add(category)
        db_session.commit()
        return category

    def test_create_category(self, repository: ComponentCategoryRepository):
        """Test creating a category through repository."""
        category_data = {
            "name": "New Category",
            "description": "New description",
            "is_active": True,
        }
        
        category = repository.create(category_data)
        
        assert category.id is not None
        assert category.name == "New Category"
        assert category.description == "New description"
        assert category.is_active is True

    def test_get_by_id(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test retrieving category by ID."""
        category = repository.get_by_id(sample_category.id)
        
        assert category is not None
        assert category.id == sample_category.id
        assert category.name == sample_category.name

    def test_get_by_name(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test retrieving category by name."""
        category = repository.get_by_name("Sample Category")
        
        assert category is not None
        assert category.id == sample_category.id
        assert category.name == "Sample Category"

    def test_get_root_categories(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test retrieving root categories."""
        categories = repository.get_root_categories()
        
        assert len(categories) >= 1
        assert any(cat.id == sample_category.id for cat in categories)
        assert all(cat.parent_category_id is None for cat in categories)

    def test_search_categories(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test searching categories."""
        search_schema = ComponentCategorySearchSchema(
            search_term="Sample",
            is_active=True,
        )
        pagination = PaginationParams(page=1, limit=10)
        
        categories, total_count = repository.search_categories(search_schema, pagination)
        
        assert total_count >= 1
        assert len(categories) >= 1
        assert any(cat.id == sample_category.id for cat in categories)

    def test_update_category(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test updating a category."""
        update_data = {
            "name": "Updated Category",
            "description": "Updated description",
        }
        
        updated_category = repository.update(sample_category.id, update_data)
        
        assert updated_category.name == "Updated Category"
        assert updated_category.description == "Updated description"

    def test_delete_category(self, repository: ComponentCategoryRepository, sample_category: ComponentCategory):
        """Test deleting a category."""
        result = repository.delete(sample_category.id)
        
        assert result is True
        
        # Verify category is soft deleted
        category = repository.get_by_id(sample_category.id)
        assert category is None  # Should not be returned due to soft delete filter
