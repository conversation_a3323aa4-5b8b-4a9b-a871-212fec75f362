#!/usr/bin/env python3
"""Component Type API Routes for Ultimate Electrical Designer.

This module provides REST API endpoints for component type management operations,
including CRUD operations, category relationships, and type classification for
electrical component catalog management.

Key Features:
- Complete CRUD operations with comprehensive validation
- Category relationship management and filtering
- Advanced search and filtering with pagination
- Specifications template management
- Professional error handling and logging
- OpenAPI documentation with detailed examples
- Authentication and authorization integration
- Performance monitoring and caching
"""

from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, Query, status
from fastapi.responses import JSONResponse

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.monitoring.unified_performance_monitor import monitor_api_performance
from src.core.schemas.error import ErrorResponseSchema
from src.core.schemas.general.component_type_schemas import (
    ComponentTypeCreateSchema,
    ComponentTypeListResponseSchema,
    ComponentTypeReadSchema,
    ComponentTypeSearchSchema,
    ComponentTypeSummarySchema,
    ComponentTypeUpdateSchema,
)
from src.core.security.enhanced_dependencies import require_authenticated_user
from src.core.services.dependencies import get_component_type_service
from src.core.services.general.component_type_service import ComponentTypeService
from src.core.utils.pagination_utils import PaginationParams

router = APIRouter(
    prefix="/component-types",
    tags=["Component Types"],
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
        status.HTTP_403_FORBIDDEN: {
            "description": "Insufficient permissions",
            "model": ErrorResponseSchema,
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "description": "Internal server error",
            "model": ErrorResponseSchema,
        },
    },
)


@router.post(
    "/",
    response_model=ComponentTypeReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="Create Component Type",
    description="Create a new component type with category relationship",
    responses={
        status.HTTP_201_CREATED: {
            "description": "Component type created successfully",
            "model": ComponentTypeReadSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid component type data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_409_CONFLICT: {
            "description": "Component type already exists",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("create_component_type")
@monitor_api_performance("create_component_type")
async def create_component_type(
    type_data: ComponentTypeCreateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    type_service: ComponentTypeService = Depends(get_component_type_service),
) -> ComponentTypeReadSchema:
    """Create a new component type.

    This endpoint creates a new component type in the electrical catalog with
    comprehensive validation and category relationship management.

    Args:
        type_data: Component type creation data
        current_user: Current authenticated user
        type_service: Component type service dependency

    Returns:
        ComponentTypeReadSchema: Created component type data
    """
    logger.info(f"Creating component type: {type_data.name}")

    component_type = type_service.create_type(type_data)
    
    logger.info(f"Created component type: {component_type.id}")
    return component_type


@router.get(
    "/{type_id}",
    response_model=ComponentTypeReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Get Component Type",
    description="Get component type by ID with full details",
    responses={
        status.HTTP_200_OK: {
            "description": "Component type retrieved successfully",
            "model": ComponentTypeReadSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Component type not found",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("get_component_type")
@monitor_api_performance("get_component_type")
async def get_component_type(
    type_id: int,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    type_service: ComponentTypeService = Depends(get_component_type_service),
) -> ComponentTypeReadSchema:
    """Get component type by ID.

    Args:
        type_id: Component type ID
        current_user: Current authenticated user
        type_service: Component type service dependency

    Returns:
        ComponentTypeReadSchema: Component type data
    """
    logger.debug(f"Retrieving component type: {type_id}")

    component_type = type_service.get_type(type_id)
    
    logger.debug(f"Retrieved component type: {type_id}")
    return component_type


@router.put(
    "/{type_id}",
    response_model=ComponentTypeReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Update Component Type",
    description="Update component type with validation",
    responses={
        status.HTTP_200_OK: {
            "description": "Component type updated successfully",
            "model": ComponentTypeReadSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Component type not found",
            "model": ErrorResponseSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid update data",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("update_component_type")
@monitor_api_performance("update_component_type")
async def update_component_type(
    type_id: int,
    type_data: ComponentTypeUpdateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    type_service: ComponentTypeService = Depends(get_component_type_service),
) -> ComponentTypeReadSchema:
    """Update component type.

    Args:
        type_id: Component type ID
        type_data: Component type update data
        current_user: Current authenticated user
        type_service: Component type service dependency

    Returns:
        ComponentTypeReadSchema: Updated component type data
    """
    logger.info(f"Updating component type: {type_id}")

    component_type = type_service.update_type(type_id, type_data)
    
    logger.info(f"Updated component type: {type_id}")
    return component_type


@router.delete(
    "/{type_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete Component Type",
    description="Soft delete component type with dependency checking",
    responses={
        status.HTTP_204_NO_CONTENT: {
            "description": "Component type deleted successfully",
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Component type not found",
            "model": ErrorResponseSchema,
        },
        status.HTTP_409_CONFLICT: {
            "description": "Component type has dependencies",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("delete_component_type")
@monitor_api_performance("delete_component_type")
async def delete_component_type(
    type_id: int,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    type_service: ComponentTypeService = Depends(get_component_type_service),
) -> JSONResponse:
    """Delete component type.

    Args:
        type_id: Component type ID
        current_user: Current authenticated user
        type_service: Component type service dependency

    Returns:
        JSONResponse: Empty response with 204 status
    """
    logger.info(f"Deleting component type: {type_id}")

    user_id = current_user.get("id")
    type_service.delete_type(type_id, user_id)
    
    logger.info(f"Deleted component type: {type_id}")
    return JSONResponse(status_code=status.HTTP_204_NO_CONTENT, content=None)


@router.get(
    "/",
    response_model=ComponentTypeListResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="List Component Types",
    description="List component types with filtering and pagination",
    responses={
        status.HTTP_200_OK: {
            "description": "Component types retrieved successfully",
            "model": ComponentTypeListResponseSchema,
        },
    },
)
@handle_api_errors("list_component_types")
@monitor_api_performance("list_component_types")
async def list_component_types(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    search_term: Optional[str] = Query(None, description="Search term"),
    category_id: Optional[int] = Query(None, description="Filter by category"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    has_specifications_template: Optional[bool] = Query(None, description="Filter by specifications template"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    type_service: ComponentTypeService = Depends(get_component_type_service),
) -> ComponentTypeListResponseSchema:
    """List component types with filtering and pagination.

    Args:
        page: Page number (1-based)
        size: Number of items per page
        search_term: Search term for name or description
        category_id: Filter by category
        is_active: Filter by active status
        has_specifications_template: Filter by presence of specifications template
        current_user: Current authenticated user
        type_service: Component type service dependency

    Returns:
        ComponentTypeListResponseSchema: Paginated component type list
    """
    logger.debug("Listing component types")

    # Build search schema
    search_schema = ComponentTypeSearchSchema(
        search_term=search_term,
        category_id=category_id,
        is_active=is_active,
        has_specifications_template=has_specifications_template,
    )

    # Build pagination
    pagination = PaginationParams(page=page, limit=size)

    # Get component types
    types = type_service.list_types(search_schema, pagination)
    
    logger.debug(f"Listed {len(types.component_types)} component types")
    return types


@router.get(
    "/by-category/{category_id}",
    response_model=List[ComponentTypeSummarySchema],
    status_code=status.HTTP_200_OK,
    summary="Get Component Types by Category",
    description="Get component types filtered by category",
    responses={
        status.HTTP_200_OK: {
            "description": "Component types retrieved successfully",
            "model": List[ComponentTypeSummarySchema],
        },
    },
)
@handle_api_errors("get_component_types_by_category")
@monitor_api_performance("get_component_types_by_category")
async def get_component_types_by_category(
    category_id: int,
    include_inactive: bool = Query(False, description="Include inactive types"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records"),
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    type_service: ComponentTypeService = Depends(get_component_type_service),
) -> List[ComponentTypeSummarySchema]:
    """Get component types by category.

    Args:
        category_id: Category ID
        include_inactive: Whether to include inactive types
        skip: Number of records to skip
        limit: Maximum number of records to return
        current_user: Current authenticated user
        type_service: Component type service dependency

    Returns:
        List[ComponentTypeSummarySchema]: Component types in category
    """
    logger.debug(f"Retrieving component types for category {category_id}")

    types = type_service.get_types_by_category(category_id, include_inactive, skip, limit)
    
    logger.debug(f"Retrieved {len(types)} component types for category {category_id}")
    return types


@router.put(
    "/{type_id}/specifications-template",
    response_model=ComponentTypeReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Update Specifications Template",
    description="Update specifications template for component type",
    responses={
        status.HTTP_200_OK: {
            "description": "Specifications template updated successfully",
            "model": ComponentTypeReadSchema,
        },
        status.HTTP_404_NOT_FOUND: {
            "description": "Component type not found",
            "model": ErrorResponseSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid template data",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("update_specifications_template")
@monitor_api_performance("update_specifications_template")
async def update_specifications_template(
    type_id: int,
    template: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    type_service: ComponentTypeService = Depends(get_component_type_service),
) -> ComponentTypeReadSchema:
    """Update specifications template for component type.

    Args:
        type_id: Component type ID
        template: New specifications template
        current_user: Current authenticated user
        type_service: Component type service dependency

    Returns:
        ComponentTypeReadSchema: Updated component type
    """
    logger.info(f"Updating specifications template for type {type_id}")

    component_type = type_service.update_specifications_template(type_id, template)
    
    logger.info(f"Updated specifications template for type {type_id}")
    return component_type
