#!/usr/bin/env python3
"""Tests for Component Type functionality.

This module provides comprehensive tests for component type management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentType model validation and business logic
- ComponentTypeRepository data access operations
- ComponentTypeService business logic and validation
- Component Type API endpoints and error handling
- Category relationships and validation
- Specifications template management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_type import ComponentType
from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_type_repository import ComponentTypeRepository
from src.core.services.general.component_type_service import ComponentTypeService
from src.core.schemas.general.component_type_schemas import (
    ComponentTypeCreateSchema,
    ComponentTypeUpdateSchema,
    ComponentTypeSearchSchema,
)
from src.core.exceptions import BusinessLogicError, NotFoundError, ValidationError
from src.core.utils.pagination import PaginationParams


class TestComponentTypeModel:
    """Test ComponentType model functionality."""

    @pytest.fixture
    def sample_category(self, db_session: Session) -> ComponentCategory:
        """Create a sample category for testing."""
        category = ComponentCategory(
            name="Test Category",
            description="Test category description",
            is_active=True,
        )
        db_session.add(category)
        db_session.commit()
        return category

    def test_component_type_creation(self, db_session: Session, sample_category: ComponentCategory):
        """Test creating a ComponentType instance."""
        component_type = ComponentType(
            name="Test Type",
            description="Test description",
            category_id=sample_category.id,
            is_active=True,
        )
        
        db_session.add(component_type)
        db_session.commit()
        
        assert component_type.id is not None
        assert component_type.name == "Test Type"
        assert component_type.description == "Test description"
        assert component_type.category_id == sample_category.id
        assert component_type.is_active is True

    def test_component_type_properties(self, db_session: Session, sample_category: ComponentCategory):
        """Test component type computed properties."""
        component_type = ComponentType(
            name="Test Type",
            description="Test description",
            category_id=sample_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.commit()
        
        assert component_type.component_count == 0
        assert component_type.has_specifications_template is False
        assert component_type.can_delete() == (True, None)

    def test_specifications_template_functionality(self, db_session: Session, sample_category: ComponentCategory):
        """Test specifications template functionality."""
        template = {
            "electrical": {
                "voltage": {"type": "number", "required": True},
                "current": {"type": "number", "required": True},
            },
            "thermal": {
                "operating_temp": {"type": "range", "min": -40, "max": 85},
            }
        }
        
        component_type = ComponentType(
            name="Test Type",
            description="Test description",
            category_id=sample_category.id,
            specifications_template=template,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.commit()
        
        assert component_type.has_specifications_template is True
        assert component_type.specifications_template == template
        
        # Test specification fields extraction
        fields = component_type.get_specification_fields()
        assert "voltage" in fields
        assert "current" in fields
        assert "operating_temp" in fields

    def test_component_type_soft_delete(self, db_session: Session, sample_category: ComponentCategory):
        """Test component type soft delete functionality."""
        component_type = ComponentType(
            name="Test Type",
            description="Test description",
            category_id=sample_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.commit()
        
        # Test successful soft delete
        result = component_type.soft_delete(user_id=1)
        assert result is True
        assert component_type.is_deleted is True
        assert component_type.deleted_at is not None
        assert component_type.deleted_by_user_id == 1
        assert component_type.is_active is False


class TestComponentTypeRepository:
    """Test ComponentTypeRepository functionality."""

    @pytest.fixture
    def repository(self, db_session: Session) -> ComponentTypeRepository:
        """Create ComponentTypeRepository instance."""
        return ComponentTypeRepository(db_session)

    @pytest.fixture
    def sample_category(self, db_session: Session) -> ComponentCategory:
        """Create a sample category for testing."""
        category = ComponentCategory(
            name="Sample Category",
            description="Sample description",
            is_active=True,
        )
        db_session.add(category)
        db_session.commit()
        return category

    @pytest.fixture
    def sample_component_type(self, db_session: Session, sample_category: ComponentCategory) -> ComponentType:
        """Create a sample component type for testing."""
        component_type = ComponentType(
            name="Sample Type",
            description="Sample description",
            category_id=sample_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.commit()
        return component_type

    def test_create_component_type(self, repository: ComponentTypeRepository, sample_category: ComponentCategory):
        """Test creating a component type through repository."""
        type_data = {
            "name": "New Type",
            "description": "New description",
            "category_id": sample_category.id,
            "is_active": True,
        }
        
        component_type = repository.create(type_data)
        
        assert component_type.id is not None
        assert component_type.name == "New Type"
        assert component_type.description == "New description"
        assert component_type.category_id == sample_category.id
        assert component_type.is_active is True

    def test_get_by_id(self, repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test retrieving component type by ID."""
        component_type = repository.get_by_id(sample_component_type.id)
        
        assert component_type is not None
        assert component_type.id == sample_component_type.id
        assert component_type.name == sample_component_type.name

    def test_get_by_name(self, repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test retrieving component type by name."""
        component_type = repository.get_by_name("Sample Type", sample_component_type.category_id)
        
        assert component_type is not None
        assert component_type.id == sample_component_type.id
        assert component_type.name == "Sample Type"

    def test_get_by_category(self, repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test retrieving component types by category."""
        types = repository.get_by_category(sample_component_type.category_id)
        
        assert len(types) >= 1
        assert any(t.id == sample_component_type.id for t in types)

    def test_search_types(self, repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test searching component types."""
        search_schema = ComponentTypeSearchSchema(
            search_term="Sample",
            category_id=sample_component_type.category_id,
            is_active=True,
        )
        pagination = PaginationParams(page=1, limit=10)
        
        types, total_count = repository.search_types(search_schema, pagination)
        
        assert total_count >= 1
        assert len(types) >= 1
        assert any(t.id == sample_component_type.id for t in types)

    def test_update_component_type(self, repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test updating a component type."""
        update_data = {
            "name": "Updated Type",
            "description": "Updated description",
        }
        
        updated_type = repository.update(sample_component_type.id, update_data)
        
        assert updated_type.name == "Updated Type"
        assert updated_type.description == "Updated description"

    def test_update_specifications_template(self, repository: ComponentTypeRepository, sample_component_type: ComponentType):
        """Test updating specifications template."""
        template = {
            "electrical": {
                "voltage": {"type": "number", "required": True},
            }
        }
        
        updated_type = repository.update_specifications_template(sample_component_type.id, template)
        
        assert updated_type is not None
        assert updated_type.specifications_template == template

    def test_validate_category_exists(self, repository: ComponentTypeRepository, sample_category: ComponentCategory):
        """Test category validation."""
        # Test existing category
        assert repository.validate_category_exists(sample_category.id) is True
        
        # Test non-existing category
        assert repository.validate_category_exists(99999) is False


class TestComponentTypeService:
    """Test ComponentTypeService functionality."""

    @pytest.fixture
    def mock_repository(self) -> Mock:
        """Create mock repository."""
        return Mock(spec=ComponentTypeRepository)

    @pytest.fixture
    def service(self, mock_repository: Mock) -> ComponentTypeService:
        """Create ComponentTypeService instance."""
        return ComponentTypeService(mock_repository)

    @pytest.fixture
    def sample_type_data(self) -> ComponentTypeCreateSchema:
        """Create sample component type data."""
        return ComponentTypeCreateSchema(
            name="Test Type",
            description="Test description",
            category_id=1,
            is_active=True,
        )

    def test_create_type_success(self, service: ComponentTypeService, mock_repository: Mock, sample_type_data: ComponentTypeCreateSchema):
        """Test successful component type creation."""
        # Setup mock
        mock_type = ComponentType(
            id=1,
            name=sample_type_data.name,
            description=sample_type_data.description,
            category_id=sample_type_data.category_id,
            is_active=sample_type_data.is_active,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        mock_repository.get_by_name.return_value = None
        mock_repository.validate_category_exists.return_value = True
        mock_repository.create.return_value = mock_type
        
        # Test creation
        result = service.create_type(sample_type_data)
        
        assert result.id == 1
        assert result.name == "Test Type"
        assert result.description == "Test description"
        mock_repository.create.assert_called_once()

    def test_create_type_duplicate_name(self, service: ComponentTypeService, mock_repository: Mock, sample_type_data: ComponentTypeCreateSchema):
        """Test component type creation with duplicate name."""
        # Setup mock to return existing type
        existing_type = ComponentType(
            id=1,
            name=sample_type_data.name,
            description="Existing description",
            category_id=sample_type_data.category_id,
            is_active=True,
        )
        mock_repository.get_by_name.return_value = existing_type
        mock_repository.validate_category_exists.return_value = True
        
        # Test creation should fail
        with pytest.raises(BusinessLogicError, match="already exists"):
            service.create_type(sample_type_data)

    def test_create_type_invalid_category(self, service: ComponentTypeService, mock_repository: Mock, sample_type_data: ComponentTypeCreateSchema):
        """Test component type creation with invalid category."""
        # Setup mock
        mock_repository.get_by_name.return_value = None
        mock_repository.validate_category_exists.return_value = False
        
        # Test creation should fail
        with pytest.raises(ValidationError, match="Category does not exist"):
            service.create_type(sample_type_data)

    def test_get_type_success(self, service: ComponentTypeService, mock_repository: Mock):
        """Test successful component type retrieval."""
        # Setup mock
        mock_type = ComponentType(
            id=1,
            name="Test Type",
            description="Test description",
            category_id=1,
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        mock_repository.get_by_id.return_value = mock_type
        
        # Test retrieval
        result = service.get_type(1)
        
        assert result.id == 1
        assert result.name == "Test Type"
        mock_repository.get_by_id.assert_called_once_with(1)

    def test_get_type_not_found(self, service: ComponentTypeService, mock_repository: Mock):
        """Test component type retrieval when not found."""
        # Setup mock to return None
        mock_repository.get_by_id.return_value = None
        
        # Test retrieval should fail
        with pytest.raises(NotFoundError, match="not found"):
            service.get_type(999)

    def test_update_specifications_template(self, service: ComponentTypeService, mock_repository: Mock):
        """Test updating specifications template."""
        # Setup mock
        existing_type = ComponentType(
            id=1,
            name="Test Type",
            description="Test description",
            category_id=1,
            is_active=True,
        )
        updated_type = ComponentType(
            id=1,
            name="Test Type",
            description="Test description",
            category_id=1,
            is_active=True,
            specifications_template={"electrical": {"voltage": {"type": "number"}}},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        
        mock_repository.get_by_id.return_value = existing_type
        mock_repository.update_specifications_template.return_value = updated_type
        
        template = {"electrical": {"voltage": {"type": "number"}}}
        
        # Test update
        result = service.update_specifications_template(1, template)
        
        assert result.specifications_template == template
        mock_repository.update_specifications_template.assert_called_once_with(1, template)


class TestComponentTypeAPI:
    """Test Component Type API endpoints."""

    @pytest.fixture
    def sample_category_id(self, client: TestClient, auth_headers: Dict[str, str]) -> int:
        """Create a sample category and return its ID."""
        category_data = {
            "name": "API Test Category",
            "description": "API test category description",
            "is_active": True,
        }
        
        response = client.post(
            "/api/v1/component-categories/",
            json=category_data,
            headers=auth_headers,
        )
        return response.json()["id"]

    def test_create_type_endpoint(self, client: TestClient, auth_headers: Dict[str, str], sample_category_id: int):
        """Test POST /component-types/ endpoint."""
        type_data = {
            "name": "API Test Type",
            "description": "API test description",
            "category_id": sample_category_id,
            "is_active": True,
        }
        
        response = client.post(
            "/api/v1/component-types/",
            json=type_data,
            headers=auth_headers,
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "API Test Type"
        assert data["description"] == "API test description"
        assert data["category_id"] == sample_category_id
        assert data["is_active"] is True

    def test_get_type_endpoint(self, client: TestClient, auth_headers: Dict[str, str], sample_category_id: int):
        """Test GET /component-types/{id} endpoint."""
        # First create a type
        type_data = {
            "name": "Get Test Type",
            "description": "Get test description",
            "category_id": sample_category_id,
            "is_active": True,
        }
        
        create_response = client.post(
            "/api/v1/component-types/",
            json=type_data,
            headers=auth_headers,
        )
        type_id = create_response.json()["id"]
        
        # Then retrieve it
        response = client.get(
            f"/api/v1/component-types/{type_id}",
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == type_id
        assert data["name"] == "Get Test Type"

    def test_list_types_endpoint(self, client: TestClient, auth_headers: Dict[str, str]):
        """Test GET /component-types/ endpoint."""
        response = client.get(
            "/api/v1/component-types/",
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "component_types" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data

    def test_get_types_by_category_endpoint(self, client: TestClient, auth_headers: Dict[str, str], sample_category_id: int):
        """Test GET /component-types/by-category/{category_id} endpoint."""
        response = client.get(
            f"/api/v1/component-types/by-category/{sample_category_id}",
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_update_specifications_template_endpoint(self, client: TestClient, auth_headers: Dict[str, str], sample_category_id: int):
        """Test PUT /component-types/{id}/specifications-template endpoint."""
        # First create a type
        type_data = {
            "name": "Template Test Type",
            "description": "Template test description",
            "category_id": sample_category_id,
            "is_active": True,
        }
        
        create_response = client.post(
            "/api/v1/component-types/",
            json=type_data,
            headers=auth_headers,
        )
        type_id = create_response.json()["id"]
        
        # Then update its template
        template = {
            "electrical": {
                "voltage": {"type": "number", "required": True},
                "current": {"type": "number", "required": True},
            }
        }
        
        response = client.put(
            f"/api/v1/component-types/{type_id}/specifications-template",
            json=template,
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["specifications_template"] == template
