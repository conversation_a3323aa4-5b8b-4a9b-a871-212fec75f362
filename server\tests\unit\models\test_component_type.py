#!/usr/bin/env python3
"""Tests for Component Type functionality.

This module provides comprehensive tests for component type management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentType model validation and business logic
- ComponentTypeRepository data access operations
- ComponentTypeService business logic and validation
- Component Type API endpoints and error handling
- Category relationships and validation
- Specifications template management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_type import ComponentType
from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_type_repository import ComponentTypeRepository
from src.core.services.general.component_type_service import ComponentTypeService
from src.core.schemas.general.component_type_schemas import (
    ComponentTypeCreateSchema,
    ComponentTypeUpdateSchema,
    ComponentTypeSearchSchema,
)
from src.core.errors.exceptions import BusinessLogicError, NotFoundError, ValidationError
from src.core.utils.pagination_utils import PaginationParams


class TestComponentTypeModel:
    """Test ComponentType model functionality."""

    @pytest.fixture
    def sample_category(self, db_session: Session) -> ComponentCategory:
        """Create a sample category for testing."""
        category = ComponentCategory(
            name="Test Category",
            description="Test category description",
            is_active=True,
        )
        db_session.add(category)
        db_session.commit()
        return category

    def test_component_type_creation(self, db_session: Session, sample_category: ComponentCategory):
        """Test creating a ComponentType instance."""
        component_type = ComponentType(
            name="Test Type",
            description="Test description",
            category_id=sample_category.id,
            is_active=True,
        )
        
        db_session.add(component_type)
        db_session.commit()
        
        assert component_type.id is not None
        assert component_type.name == "Test Type"
        assert component_type.description == "Test description"
        assert component_type.category_id == sample_category.id
        assert component_type.is_active is True

    def test_component_type_properties(self, db_session: Session, sample_category: ComponentCategory):
        """Test component type computed properties."""
        component_type = ComponentType(
            name="Test Type",
            description="Test description",
            category_id=sample_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.commit()
        
        assert component_type.component_count == 0
        assert component_type.has_specifications_template is False
        assert component_type.can_delete() == (True, None)

    def test_specifications_template_functionality(self, db_session: Session, sample_category: ComponentCategory):
        """Test specifications template functionality."""
        template = {
            "electrical": {
                "voltage": {"type": "number", "required": True},
                "current": {"type": "number", "required": True},
            },
            "thermal": {
                "operating_temp": {"type": "range", "min": -40, "max": 85},
            }
        }
        
        component_type = ComponentType(
            name="Test Type",
            description="Test description",
            category_id=sample_category.id,
            specifications_template=template,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.commit()
        
        assert component_type.has_specifications_template is True
        assert component_type.specifications_template == template
        
        # Test specification fields extraction
        fields = component_type.get_specification_fields()
        assert "voltage" in fields
        assert "current" in fields
        assert "operating_temp" in fields

    def test_component_type_soft_delete(self, db_session: Session, sample_category: ComponentCategory):
        """Test component type soft delete functionality."""
        component_type = ComponentType(
            name="Test Type",
            description="Test description",
            category_id=sample_category.id,
            is_active=True,
        )
        db_session.add(component_type)
        db_session.commit()
        
        # Test successful soft delete
        result = component_type.soft_delete(user_id=1)
        assert result is True
        assert component_type.is_deleted is True
        assert component_type.deleted_at is not None
        assert component_type.deleted_by_user_id == 1
        assert component_type.is_active is False
