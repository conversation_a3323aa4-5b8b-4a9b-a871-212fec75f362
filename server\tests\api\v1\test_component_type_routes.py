#!/usr/bin/env python3
"""Tests for Component Type functionality.

This module provides comprehensive tests for component type management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentType model validation and business logic
- ComponentTypeRepository data access operations
- ComponentTypeService business logic and validation
- Component Type API endpoints and error handling
- Category relationships and validation
- Specifications template management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_type import ComponentType
from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_type_repository import ComponentTypeRepository
from src.core.services.general.component_type_service import ComponentTypeService
from src.core.schemas.general.component_type_schemas import (
    ComponentTypeCreateSchema,
    ComponentTypeUpdateSchema,
    ComponentTypeSearchSchema,
)
from src.core.errors.exceptions import BusinessLogicError, NotFoundError, ValidationError
from src.core.utils.pagination_utils import PaginationParams

class TestComponentTypeAPI:
    """Test Component Type API endpoints."""

    @pytest.fixture
    def sample_category_id(self, client: TestClient, auth_headers: Dict[str, str]) -> int:
        """Create a sample category and return its ID."""
        category_data = {
            "name": "API Test Category",
            "description": "API test category description",
            "is_active": True,
        }
        
        response = client.post(
            "/api/v1/component-categories/",
            json=category_data,
            headers=auth_headers,
        )
        return response.json()["id"]

    def test_create_type_endpoint(self, client: TestClient, auth_headers: Dict[str, str], sample_category_id: int):
        """Test POST /component-types/ endpoint."""
        type_data = {
            "name": "API Test Type",
            "description": "API test description",
            "category_id": sample_category_id,
            "is_active": True,
        }
        
        response = client.post(
            "/api/v1/component-types/",
            json=type_data,
            headers=auth_headers,
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "API Test Type"
        assert data["description"] == "API test description"
        assert data["category_id"] == sample_category_id
        assert data["is_active"] is True

    def test_get_type_endpoint(self, client: TestClient, auth_headers: Dict[str, str], sample_category_id: int):
        """Test GET /component-types/{id} endpoint."""
        # First create a type
        type_data = {
            "name": "Get Test Type",
            "description": "Get test description",
            "category_id": sample_category_id,
            "is_active": True,
        }
        
        create_response = client.post(
            "/api/v1/component-types/",
            json=type_data,
            headers=auth_headers,
        )
        type_id = create_response.json()["id"]
        
        # Then retrieve it
        response = client.get(
            f"/api/v1/component-types/{type_id}",
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == type_id
        assert data["name"] == "Get Test Type"

    def test_list_types_endpoint(self, client: TestClient, auth_headers: Dict[str, str]):
        """Test GET /component-types/ endpoint."""
        response = client.get(
            "/api/v1/component-types/",
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "component_types" in data
        assert "total_count" in data
        assert "page" in data
        assert "page_size" in data

    def test_get_types_by_category_endpoint(self, client: TestClient, auth_headers: Dict[str, str], sample_category_id: int):
        """Test GET /component-types/by-category/{category_id} endpoint."""
        response = client.get(
            f"/api/v1/component-types/by-category/{sample_category_id}",
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)

    def test_update_specifications_template_endpoint(self, client: TestClient, auth_headers: Dict[str, str], sample_category_id: int):
        """Test PUT /component-types/{id}/specifications-template endpoint."""
        # First create a type
        type_data = {
            "name": "Template Test Type",
            "description": "Template test description",
            "category_id": sample_category_id,
            "is_active": True,
        }
        
        create_response = client.post(
            "/api/v1/component-types/",
            json=type_data,
            headers=auth_headers,
        )
        type_id = create_response.json()["id"]
        
        # Then update its template
        template = {
            "electrical": {
                "voltage": {"type": "number", "required": True},
                "current": {"type": "number", "required": True},
            }
        }
        
        response = client.put(
            f"/api/v1/component-types/{type_id}/specifications-template",
            json=template,
            headers=auth_headers,
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["specifications_template"] == template
